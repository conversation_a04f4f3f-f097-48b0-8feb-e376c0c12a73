# 华为云环境部署指南

## 前提条件

假设您已在华为云环境中安装了以下组件：
- PaddleOCR 3.0.0
- PaddlePaddle (CPU或GPU版本)
- PaddleX 3.0.0

## 快速部署步骤

### 1. 上传项目文件

将整个项目目录上传到华为云服务器：

```bash
# 解压项目文件
tar -xzf PaddleOCRFastAPI-PP-OCRv5.tar.gz
cd PaddleOCRFastAPI-PP-OCRv5
```

### 2. 安装最小依赖

由于PaddleOCR已预装，只需安装FastAPI相关依赖：

```bash
# 安装最小依赖集
pip install -r requirements_minimal.txt
```

### 3. 验证环境

```bash
# 验证PaddleOCR是否可用
python3 -c "from paddleocr import PaddleOCR; print('PaddleOCR OK')"

# 验证FastAPI是否可用
python3 -c "import fastapi, uvicorn; print('FastAPI OK')"
```

### 4. 启动服务

```bash
# 生产环境启动
./start.sh

# 或开发环境启动（带自动重载）
./start_dev.sh
```

### 5. 配置环境变量（可选）

```bash
# 设置服务监听地址和端口
export OCR_HOST="0.0.0.0"      # 监听所有网络接口
export OCR_PORT="8000"         # 服务端口
export OCR_WORKERS="4"         # 工作进程数（仅生产环境）

# 启动服务
./start.sh
```

## 文件说明

### 依赖文件
- `requirements_minimal.txt` - 最小依赖集（推荐用于华为云）
- `requirements.txt` - 完整依赖集（如果存在，包含所有依赖）

### 启动脚本
- `start.sh` - 生产环境启动脚本
- `start_dev.sh` - 开发环境启动脚本
- `start.bat` - Windows启动脚本
- `start.command` - macOS启动脚本

### 配置文件
- `ocr_config.yaml` - OCR模型配置
- `DEPLOYMENT.md` - 通用部署指南
- `README.md` - 项目说明文档

## 服务验证

启动后，可以通过以下方式验证服务：

```bash
# 检查服务状态
curl http://localhost:8000/docs

# 测试OCR接口
curl -X POST "http://localhost:8000/ocr" \
  -H "accept: application/json" \
  -F "file=@test_image.jpg"
```

## 故障排除

1. **权限问题**：
   ```bash
   chmod +x start.sh start_dev.sh
   ```

2. **端口占用**：
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8000
   
   # 或使用其他端口
   export OCR_PORT="8001"
   ./start.sh
   ```

3. **依赖问题**：
   ```bash
   # 检查Python环境
   python3 --version
   pip list | grep -E "(fastapi|uvicorn|paddleocr)"
   ```

## 性能优化建议

1. **GPU加速**：确保ocr_config.yaml中device设置正确
2. **并发控制**：根据服务器配置调整OCR_WORKERS数量
3. **内存管理**：监控内存使用，必要时重启服务
