# 华为云NPU环境部署指南

## 前提条件

假设您已在华为云NPU环境中安装了以下组件：
- PaddleOCR 3.0.0 (NPU版本)
- PaddlePaddle NPU版本 (paddle-custom-npu==3.0.0.dev20250527)
- NPU相关驱动和工具链
- HCCL (华为集合通信库)

## 快速部署步骤

### 1. 上传项目文件

将整个项目目录上传到华为云服务器：

```bash
# 解压项目文件
tar -xzf PaddleOCRFastAPI-PP-OCRv5.tar.gz
cd PaddleOCRFastAPI-PP-OCRv5
```

### 2. 安装最小依赖

由于PaddleOCR已预装，只需安装FastAPI相关依赖：

```bash
# 安装最小依赖集
pip install -r requirements_minimal.txt
```

### 3. 验证NPU环境

```bash
# 运行环境检测脚本
python3 check_npu_env.py

# 或手动验证各组件
python3 -c "from paddleocr import PaddleOCR; print('PaddleOCR OK')"
python3 -c "import fastapi, uvicorn; print('FastAPI OK')"
python3 -c "import paddle; print('NPU available:', paddle.device.is_compiled_with_custom_device('npu'))"
```

### 4. 启动服务

```bash
# 生产环境启动
./start.sh

# 或开发环境启动（带自动重载）
./start_dev.sh
```

### 5. 配置环境变量（可选）

```bash
# 设置服务监听地址和端口
export OCR_HOST="0.0.0.0"      # 监听所有网络接口
export OCR_PORT="8000"         # 服务端口
export OCR_WORKERS="4"         # 工作进程数（仅生产环境）

# 启动服务
./start.sh
```

## 文件说明

### 依赖文件
- `requirements_minimal.txt` - 最小依赖集（推荐用于华为云）
- `requirements.txt` - 完整依赖集（如果存在，包含所有依赖）

### 启动脚本
- `start.sh` - 生产环境启动脚本
- `start_dev.sh` - 开发环境启动脚本
- `start.bat` - Windows启动脚本
- `start.command` - macOS启动脚本

### 配置文件
- `ocr_config.yaml` - OCR模型配置
- `DEPLOYMENT.md` - 通用部署指南
- `README.md` - 项目说明文档

## 服务验证

启动后，可以通过以下方式验证服务：

```bash
# 检查服务状态
curl http://localhost:8000/docs

# 测试OCR接口
curl -X POST "http://localhost:8000/ocr" \
  -H "accept: application/json" \
  -F "file=@test_image.jpg"
```

## 故障排除

1. **权限问题**：
   ```bash
   chmod +x start.sh start_dev.sh
   ```

2. **端口占用**：
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8000
   
   # 或使用其他端口
   export OCR_PORT="8001"
   ./start.sh
   ```

3. **依赖问题**：
   ```bash
   # 检查Python环境
   python3 --version
   pip list | grep -E "(fastapi|uvicorn|paddleocr)"
   ```

## NPU性能优化建议

1. **NPU配置**：
   - 确保ocr_config.yaml中device设置为'npu:0'
   - 根据NPU数量调整设备配置，如'npu:0,1,2,3'

2. **并发控制**：
   - 根据NPU性能调整OCR_WORKERS数量
   - NPU通常比GPU有更好的并发处理能力

3. **内存管理**：
   - 监控NPU内存使用情况
   - 必要时调整batch_size参数

4. **NPU特定优化**：
   - 启用高性能推理：enable_hpi: true
   - 根据模型大小调整NPU内存分配

## NPU环境变量

```bash
# 设置NPU相关环境变量（如果需要）
export ASCEND_RT_PATH=/usr/local/Ascend/runtime
export LD_LIBRARY_PATH=$ASCEND_RT_PATH/lib64:$LD_LIBRARY_PATH

# 启动服务
./start.sh
```
