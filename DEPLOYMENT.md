# Linux服务器部署指南

## 文件重命名说明

项目中的中文文件名已全部改为英文：

| 原文件名 | 新文件名 |
|---------|---------|
| 官网示例.py | official_example.py |
| 本地媒体文件识别.py | local_media_recognition.py |
| 获取基础配置.py | get_basic_config.py |
| Base64还原图像.html | base64_image_restore.html |

## 启动脚本说明

项目提供了多个启动脚本：

- `start.sh` - Linux生产环境启动脚本
- `start_dev.sh` - Linux开发环境启动脚本（带自动重载）
- `start.bat` - Windows启动脚本
- `start.command` - macOS启动脚本

## Linux服务器部署步骤

### 1. 环境准备

```bash
# 确保Python 3.8+已安装
python3 --version

# 安装PaddlePaddle (根据您的CUDA版本选择)
# CPU版本:
pip install paddlepaddle

# GPU版本 (CUDA 11.2):
pip install paddlepaddle-gpu

# GPU版本 (CUDA 12.0):
pip install paddlepaddle-gpu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cu120/
```

### 2. 安装依赖

```bash
# 进入项目目录
cd /path/to/your/project

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 配置权限

```bash
# 给启动脚本执行权限
chmod +x start.sh start_dev.sh
```

### 4. 启动服务

#### 开发环境启动
```bash
./start_dev.sh
```

#### 生产环境启动
```bash
./start.sh
```

#### 自定义配置启动
```bash
# 设置环境变量
export OCR_HOST="0.0.0.0"    # 监听所有网络接口
export OCR_PORT="8000"       # 端口号
export OCR_WORKERS="4"       # 工作进程数（仅生产环境）

# 启动
./start.sh
```

### 5. 服务验证

```bash
# 检查服务是否启动
curl http://localhost:8000/docs

# 测试OCR接口
curl -X POST "http://localhost:8000/ocr" \
  -H "accept: application/json" \
  -F "file=@test_image.jpg"
```

## 生产环境建议

1. **使用进程管理器**：
   ```bash
   # 使用systemd
   sudo systemctl enable ocr-service
   sudo systemctl start ocr-service
   
   # 或使用supervisor
   supervisorctl start ocr-service
   ```

2. **反向代理**：
   使用Nginx作为反向代理，配置SSL证书

3. **资源监控**：
   监控CPU、内存和GPU使用情况

4. **日志管理**：
   配置日志轮转和集中日志收集

## 故障排除

1. **权限问题**：确保脚本有执行权限
2. **端口占用**：检查端口是否被其他服务占用
3. **依赖缺失**：确保所有Python包都已正确安装
4. **模型文件**：确保models目录下的模型文件完整
