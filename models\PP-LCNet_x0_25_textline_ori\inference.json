{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "linear_0.b_0_deepcopy_144"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_0.w_0_deepcopy_143"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_2_deepcopy_142"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_1_deepcopy_141"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.b_0_deepcopy_140"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_0_deepcopy_139"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_30.w_0_deepcopy_138"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.b_0_deepcopy_137"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.w_0_deepcopy_136"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.b_0_deepcopy_135"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.w_0_deepcopy_134"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_2_deepcopy_133"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_1_deepcopy_132"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.b_0_deepcopy_131"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_0_deepcopy_130"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.w_0_deepcopy_129"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_2_deepcopy_128"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_1_deepcopy_127"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.b_0_deepcopy_126"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_0_deepcopy_125"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.w_0_deepcopy_124"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.b_0_deepcopy_123"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.w_0_deepcopy_122"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 16, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.b_0_deepcopy_121"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.w_0_deepcopy_120"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_2_deepcopy_119"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_1_deepcopy_118"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.b_0_deepcopy_117"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_0_deepcopy_116"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.w_0_deepcopy_115"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_2_deepcopy_114"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_1_deepcopy_113"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.b_0_deepcopy_112"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_0_deepcopy_111"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.w_0_deepcopy_110"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_2_deepcopy_109"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_1_deepcopy_108"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.b_0_deepcopy_107"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_0_deepcopy_106"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.w_0_deepcopy_105"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_2_deepcopy_104"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_1_deepcopy_103"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.b_0_deepcopy_102"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_0_deepcopy_101"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.w_0_deepcopy_100"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_2_deepcopy_99"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_1_deepcopy_98"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.b_0_deepcopy_97"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_0_deepcopy_96"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.w_0_deepcopy_95"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_2_deepcopy_94"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_1_deepcopy_93"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.b_0_deepcopy_92"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_0_deepcopy_91"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.w_0_deepcopy_90"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_2_deepcopy_89"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_1_deepcopy_88"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.b_0_deepcopy_87"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_0_deepcopy_86"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.w_0_deepcopy_85"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_2_deepcopy_84"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_1_deepcopy_83"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.b_0_deepcopy_82"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_0_deepcopy_81"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.w_0_deepcopy_80"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_2_deepcopy_79"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_1_deepcopy_78"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.b_0_deepcopy_77"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_0_deepcopy_76"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.w_0_deepcopy_75"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_2_deepcopy_74"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_1_deepcopy_73"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.b_0_deepcopy_72"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_0_deepcopy_71"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.w_0_deepcopy_70"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_2_deepcopy_69"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_1_deepcopy_68"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.b_0_deepcopy_67"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_0_deepcopy_66"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.w_0_deepcopy_65"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_2_deepcopy_64"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_1_deepcopy_63"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.b_0_deepcopy_62"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_0_deepcopy_61"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.w_0_deepcopy_60"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_2_deepcopy_59"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_1_deepcopy_58"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.b_0_deepcopy_57"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_0_deepcopy_56"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.w_0_deepcopy_55"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_2_deepcopy_54"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_1_deepcopy_53"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.b_0_deepcopy_52"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_0_deepcopy_51"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.w_0_deepcopy_50"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_2_deepcopy_49"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_1_deepcopy_48"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.b_0_deepcopy_47"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_0_deepcopy_46"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.w_0_deepcopy_45"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_2_deepcopy_44"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_1_deepcopy_43"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.b_0_deepcopy_42"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_0_deepcopy_41"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.w_0_deepcopy_40"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 16, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_2_deepcopy_39"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_1_deepcopy_38"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.b_0_deepcopy_37"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_0_deepcopy_36"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.w_0_deepcopy_35"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_2_deepcopy_34"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_1_deepcopy_33"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.b_0_deepcopy_32"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_0_deepcopy_31"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.w_0_deepcopy_30"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 16, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_2_deepcopy_29"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_1_deepcopy_28"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.b_0_deepcopy_27"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_0_deepcopy_26"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.w_0_deepcopy_25"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_2_deepcopy_24"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_1_deepcopy_23"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.b_0_deepcopy_22"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_0_deepcopy_21"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.w_0_deepcopy_20"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 8, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_2_deepcopy_19"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_1_deepcopy_18"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.b_0_deepcopy_17"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_0_deepcopy_16"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.w_0_deepcopy_15"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_2_deepcopy_14"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_1_deepcopy_13"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.b_0_deepcopy_12"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_0_deepcopy_11"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.w_0_deepcopy_10"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8, 8, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_2_deepcopy_9"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_1_deepcopy_8"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.b_0_deepcopy_7"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_0_deepcopy_6"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_1.w_0_deepcopy_5"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2_deepcopy_4"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1_deepcopy_3"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0_deepcopy_2"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0_deepcopy_1"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0_deepcopy_0"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, 80, 160]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, 80, 160], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 146}, {"%": 145}], "O": [{"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 147}, {"%": 142}, {"%": 141}, {"%": 144}, {"%": 143}], "O": [{"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}, {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 148}], "O": [{"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 8}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 154}, {"%": 140}], "O": [{"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 155}, {"%": 137}, {"%": 136}, {"%": 139}, {"%": 138}], "O": [{"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}, {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 156}], "O": [{"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 162}, {"%": 135}], "O": [{"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 163}, {"%": 132}, {"%": 131}, {"%": 134}, {"%": 133}], "O": [{"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}, {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential/DepthwiseSeparable/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 164}], "O": [{"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 80, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 8}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 170}, {"%": 130}], "O": [{"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 171}, {"%": 127}, {"%": 126}, {"%": 129}, {"%": 128}], "O": [{"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 40, 80], "NCHW", [], 0]}}, {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [8], "NCHW", [], 0]}}, {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 172}], "O": [{"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 178}, {"%": 125}], "O": [{"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 179}, {"%": 122}, {"%": 121}, {"%": 124}, {"%": 123}], "O": [{"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}, {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 180}], "O": [{"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 16}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 186}, {"%": 120}], "O": [{"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 187}, {"%": 117}, {"%": 116}, {"%": 119}, {"%": 118}], "O": [{"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}, {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 188}], "O": [{"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 194}, {"%": 115}], "O": [{"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 195}, {"%": 112}, {"%": 111}, {"%": 114}, {"%": 113}], "O": [{"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}, {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_1/DepthwiseSeparable_1/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 196}], "O": [{"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 40, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 16}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 202}, {"%": 110}], "O": [{"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 203}, {"%": 107}, {"%": 106}, {"%": 109}, {"%": 108}], "O": [{"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 20, 80], "NCHW", [], 0]}}, {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 204}], "O": [{"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 210}, {"%": 105}], "O": [{"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 211}, {"%": 102}, {"%": 101}, {"%": 104}, {"%": 103}], "O": [{"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}, {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 212}], "O": [{"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 32}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 218}, {"%": 100}], "O": [{"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 219}, {"%": 97}, {"%": 96}, {"%": 99}, {"%": 98}], "O": [{"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}, {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 220}], "O": [{"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 226}, {"%": 95}], "O": [{"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 227}, {"%": 92}, {"%": 91}, {"%": 94}, {"%": 93}], "O": [{"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}, {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_2/DepthwiseSeparable_1/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 228}], "O": [{"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 20, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 32}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 234}, {"%": 90}], "O": [{"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 235}, {"%": 87}, {"%": 86}, {"%": 89}, {"%": 88}], "O": [{"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 10, 80], "NCHW", [], 0]}}, {"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 236}], "O": [{"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 242}, {"%": 85}], "O": [{"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 243}, {"%": 82}, {"%": 81}, {"%": 84}, {"%": 83}], "O": [{"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 244}], "O": [{"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 250}, {"%": 80}], "O": [{"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 251}, {"%": 77}, {"%": 76}, {"%": 79}, {"%": 78}], "O": [{"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 252}], "O": [{"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 258}, {"%": 75}], "O": [{"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 259}, {"%": 72}, {"%": 71}, {"%": 74}, {"%": 73}], "O": [{"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_1/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 260}], "O": [{"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 266}, {"%": 70}], "O": [{"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 267}, {"%": 67}, {"%": 66}, {"%": 69}, {"%": 68}], "O": [{"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 268}], "O": [{"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 274}, {"%": 65}], "O": [{"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 275}, {"%": 62}, {"%": 61}, {"%": 64}, {"%": 63}], "O": [{"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_2/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 276}], "O": [{"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 282}, {"%": 60}], "O": [{"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 283}, {"%": 57}, {"%": 56}, {"%": 59}, {"%": 58}], "O": [{"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 284}], "O": [{"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 290}, {"%": 55}], "O": [{"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 291}, {"%": 52}, {"%": 51}, {"%": 54}, {"%": 53}], "O": [{"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_3/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 292}], "O": [{"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 298}, {"%": 50}], "O": [{"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 299}, {"%": 47}, {"%": 46}, {"%": 49}, {"%": 48}], "O": [{"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 300}], "O": [{"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 306}, {"%": 45}], "O": [{"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 307}, {"%": 42}, {"%": 41}, {"%": 44}, {"%": 43}], "O": [{"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_4/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 308}], "O": [{"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 314}, {"%": 40}], "O": [{"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 315}, {"%": 37}, {"%": 36}, {"%": 39}, {"%": 38}], "O": [{"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 316}], "O": [{"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 322}, {"%": 35}], "O": [{"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 323}, {"%": 32}, {"%": 31}, {"%": 34}, {"%": 33}], "O": [{"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}, {"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_3/DepthwiseSeparable_5/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 324}], "O": [{"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 10, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 330}, {"%": 30}], "O": [{"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 331}, {"%": 27}, {"%": 26}, {"%": 29}, {"%": 28}], "O": [{"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 5, 80], "NCHW", [], 0]}}, {"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 332}], "O": [{"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 338}, {"%": 339}], "O": [{"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 340}, {"%": 25}], "O": [{"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 24}, {"%": 342}], "O": [{"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 341}, {"%": 343}], "O": [{"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/ReLU/"}, "N": "struct_name"}], "I": [{"%": 344}], "O": [{"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 345}, {"%": 23}], "O": [{"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 22}, {"%": 347}], "O": [{"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 346}, {"%": 348}], "O": [{"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 349}], "O": [{"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/SEModule/"}, "N": "struct_name"}], "I": [{"%": 338}, {"%": 350}], "O": [{"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 351}, {"%": 21}], "O": [{"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 352}, {"%": 18}, {"%": 17}, {"%": 20}, {"%": 19}], "O": [{"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}, {"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 353}], "O": [{"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 359}, {"%": 16}], "O": [{"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 360}, {"%": 13}, {"%": 12}, {"%": 15}, {"%": 14}], "O": [{"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}, {"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 361}], "O": [{"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 368}], "O": [{"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 369}, {"%": 11}], "O": [{"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 10}, {"%": 371}], "O": [{"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 370}, {"%": 372}], "O": [{"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/ReLU/"}, "N": "struct_name"}], "I": [{"%": 373}], "O": [{"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 374}, {"%": 9}], "O": [{"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 8}, {"%": 376}], "O": [{"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 375}, {"%": 377}], "O": [{"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 378}], "O": [{"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/SEModule/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 379}], "O": [{"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 380}, {"%": 7}], "O": [{"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 381}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}, {"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Sequential_4/DepthwiseSeparable_1/ConvBNLayer_1/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 382}], "O": [{"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 5, 80], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 388}, {"%": 389}], "O": [{"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.flatten", "A": [{"AT": {"#": "0.a_i32", "D": 1}, "N": "start_axis"}, {"AT": {"#": "0.a_i32", "D": 3}, "N": "stop_axis"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Flatten/"}, "N": "struct_name"}], "I": [{"%": 390}], "O": [{"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/PPLCNet/Linear/"}, "N": "struct_name"}], "I": [{"%": 391}, {"%": 2}], "O": [{"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNet/Linear/"}, "N": "struct_name"}], "I": [{"%": 392}, {"%": 1}], "O": [{"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/Softmax/"}, "N": "struct_name"}], "I": [{"%": 393}], "O": [{"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 394}], "O": [{"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}