#!/bin/bash

# PaddleOCR FastAPI Development Server Startup Script
# This script starts the OCR server in development mode with auto-reload

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed or not in PATH"
    exit 1
fi

# Check if uvicorn is available
if ! python3 -c "import uvicorn" &> /dev/null; then
    echo "Error: uvicorn is not installed. Please install requirements first:"
    echo "pip install -r requirements.txt"
    exit 1
fi

# Set environment variables
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# Development configuration
HOST=${OCR_HOST:-"127.0.0.1"}
PORT=${OCR_PORT:-"8000"}

echo "Starting PaddleOCR FastAPI Development Server..."
echo "Host: $HOST"
echo "Port: $PORT"
echo "Working directory: $SCRIPT_DIR"
echo "Auto-reload: enabled"
echo ""

# Start the server in development mode with auto-reload
uvicorn ocr_server:app --host "$HOST" --port "$PORT" --reload
