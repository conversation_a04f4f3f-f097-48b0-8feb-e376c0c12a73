#!/bin/bash

# PaddleOCR FastAPI Server Startup Script
# This script starts the OCR server on Linux

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 is not installed or not in PATH"
    exit 1
fi

# Check if uvicorn is available
if ! python3 -c "import uvicorn" &> /dev/null; then
    echo "Error: uvicorn is not installed. Please install requirements first:"
    echo "pip install -r requirements_minimal.txt"
    exit 1
fi

# Check NPU environment (optional)
echo "Checking NPU environment..."
if python3 -c "import paddle; print('NPU available:', paddle.device.is_compiled_with_custom_device('npu'))" 2>/dev/null; then
    echo "✅ NPU environment check passed"
else
    echo "⚠️  NPU environment check failed, but continuing..."
fi

# Set environment variables for production
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# Default configuration
HOST=${OCR_HOST:-"0.0.0.0"}
PORT=${OCR_PORT:-"8000"}
WORKERS=${OCR_WORKERS:-"1"}

echo "Starting PaddleOCR FastAPI Server..."
echo "Host: $HOST"
echo "Port: $PORT"
echo "Workers: $WORKERS"
echo "Working directory: $SCRIPT_DIR"
echo ""

# Start the server
# For production, remove --reload and consider using --workers for multiple processes
uvicorn ocr_server:app --host "$HOST" --port "$PORT" --workers "$WORKERS"
