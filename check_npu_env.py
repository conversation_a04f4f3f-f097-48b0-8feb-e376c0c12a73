#!/usr/bin/env python3
"""
华为云NPU环境检测脚本
用于验证NPU环境和PaddleOCR是否正确配置
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_paddle_installation():
    """检查PaddlePaddle安装"""
    print("\n=== PaddlePaddle环境检查 ===")
    try:
        import paddle
        print(f"✅ PaddlePaddle版本: {paddle.__version__}")
        
        # 检查NPU设备
        if paddle.device.is_compiled_with_custom_device('npu'):
            print("✅ PaddlePaddle支持NPU")
            
            # 获取NPU设备数量
            npu_count = paddle.device.custom.device_count('npu')
            print(f"✅ 检测到 {npu_count} 个NPU设备")
            
            return True
        else:
            print("❌ PaddlePaddle不支持NPU")
            return False
            
    except ImportError:
        print("❌ PaddlePaddle未安装")
        return False
    except Exception as e:
        print(f"❌ PaddlePaddle检查失败: {e}")
        return False

def check_paddleocr_installation():
    """检查PaddleOCR安装"""
    print("\n=== PaddleOCR环境检查 ===")
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        
        # 尝试初始化OCR引擎
        print("正在初始化OCR引擎...")
        ocr = PaddleOCR(use_angle_cls=False, use_gpu=False, lang='ch')
        print("✅ PaddleOCR初始化成功")
        return True
        
    except ImportError:
        print("❌ PaddleOCR未安装")
        return False
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        return False

def check_fastapi_dependencies():
    """检查FastAPI相关依赖"""
    print("\n=== FastAPI依赖检查 ===")
    dependencies = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
    ]
    
    all_ok = True
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} 已安装")
        except ImportError:
            print(f"❌ {name} 未安装")
            all_ok = False
    
    return all_ok

def check_config_file():
    """检查配置文件"""
    print("\n=== 配置文件检查 ===")
    config_file = "ocr_config.yaml"
    
    if os.path.exists(config_file):
        print(f"✅ 配置文件存在: {config_file}")
        
        # 检查配置内容
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'npu:0' in content:
                    print("✅ 配置文件已设置为使用NPU")
                elif 'gpu:0' in content:
                    print("⚠️  配置文件仍设置为使用GPU，建议改为NPU")
                else:
                    print("ℹ️  配置文件使用CPU模式")
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
            return False
        
        return True
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False

def main():
    """主检查函数"""
    print("华为云NPU环境检测开始...\n")
    
    checks = [
        check_python_version(),
        check_paddle_installation(),
        check_paddleocr_installation(),
        check_fastapi_dependencies(),
        check_config_file()
    ]
    
    print("\n=== 检查结果汇总 ===")
    if all(checks):
        print("🎉 所有检查通过！环境配置正确，可以启动OCR服务")
        print("\n启动命令:")
        print("  开发环境: ./start_dev.sh")
        print("  生产环境: ./start.sh")
    else:
        print("❌ 部分检查失败，请根据上述提示修复问题")
        sys.exit(1)

if __name__ == "__main__":
    main()
